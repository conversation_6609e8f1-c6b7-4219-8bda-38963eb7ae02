import baseStyles from "@/pages/FATWall/FATWall_APP/index.module.scss";
import { useHistory, useRouteMatch } from "react-router-dom";
import NavigatorBar from "@/components/NavBar";
import { IListCard } from "@/components/ListCard";
import FilmFilter from "../../../../../components/FATWall_APP/FilmFilter";
import Pull2Refresh from "@/components/Pull2Refresh";
import FilterFilmCard from "../../../../../components/FATWall_APP/FilterFilmCard";
import FloatPanel from "@/components/FloatPanel";
import { PreloadImage } from "@/components/Image";
import { CheckList, Popover } from "antd-mobile";
import styles from '../index.module.scss';
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { CheckListValue } from "antd-mobile/es/components/check-list";
import { useTheme } from "@/utils/themeDetector";

import close from '@/Resources/icon/close.png';
import close_dark from '@/Resources/icon/close_white.png';
import moreIcon from "@/Resources/filmWall/more.png";
import moreIcon_dark from "@/Resources/filmWall/more_dark.png";
import filterIcon from "@/Resources/filmWall/filter.png";
import filterIcon_dark from "@/Resources/filmWall/filter_dark.png";
import { defaultFiltersByApp, filterItemType, filterTypeList } from "@/components/FATWall_APP/FATWALL_CONST";
import { mediaProps, getMediaListFromLib } from "@/api/fatWall";
import { handleFilter } from "..";
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import FATErrorComponents from "@/pages/FATWall/FATWall_PC/Error";
import { defaultPageParam } from "../../Recently";

const FATLibrary = () => {
  const [collapse, setCollapse] = useState<boolean>(false);
  const [filters, setFilters] = useState<{ [key: string]: string }>(defaultFiltersByApp);
  const { isDarkMode } = useTheme();
  const [showFloatPanel, setShowFloatPanel] = useState(false); // 控制浮动面板
  const containerRef = useRef<HTMLDivElement | null>(null); // 滚动容器
  const [moreVisible, setMoreVisible] = useState<boolean>(false);
  const history = useHistory<IListCard & { lib_id: number; fromLibrary?: boolean; libraryTitle?: string; classes?: string; media_id?: number }>();
  const state = history.location.state;
  const routeMatch = useRouteMatch();
  const prefix = routeMatch.path.split('/')[1];
  const [medias, setMedias] = useState<mediaProps[]>([]);
  const [filterItem, setFilterItem] = useState<filterItemType>({ sort_type: 0, asc: 0 });
  const filterItemRef = useRef<filterItemType>({ sort_type: 0, asc: 0 });
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam); // 分页参数
  const prevFilters = useRef<{ [key: string]: string }>(defaultFiltersByApp); // 记录之前的筛选条件，用于判断筛选条件是否变化

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  const { runAsync: getMediasFromLib } = useRequest(getMediaListFromLib, { manual: true }); // 根据媒体库获取所有资源
  const [isError, setIsError] = useState<boolean>(false); // 是否出现错误信息

  // 媒体库数据
  const filterFilm = useMemo(() => {
    if (!state || !state.lib_id) return [];
    return medias.map((item) => {
      return { label: item.trans_name, score: item.score || 0, time: `${item.year}`, cover: item.poster.length > 0 ? item.poster[0] : '', isLike: item.favourite, lib_id: state.lib_id, media_id: item.media_id, classes: item.classes }
    })
  }, [medias, state])

  const initMediaInfo = useCallback(async (callback: (data: mediaProps[]) => void, filter?) => {
    const mediaRes = await getMediasFromLib({ lib_id: state.lib_id, filter: { ...pageOptRef.current, ...filterItemRef.current, ...filter } }).catch((e) => console.log('获取媒体库影视列表失败：', e));
    if (mediaRes && mediaRes.code === 0 && mediaRes.data) {
      if (mediaRes.data.count < pageOptRef.current.limit) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      callback(mediaRes.data.medias); // 根据是筛选还是改变页数更新状态
      setIsError(false);
      return;
    }
    setIsError(true);
  }, [getMediasFromLib, state.lib_id])

  // 初始化数据
  useEffect(() => {
    initMediaInfo((data) => setMedias(data), handleFilter(defaultFiltersByApp));
  }, [initMediaInfo])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      pageOptRef.current = { ...pageOptRef.current, offset: pageOptRef.current.offset + pageOptRef.current.limit };
      initMediaInfo((data) => setMedias(p => [...p, ...data]), handleFilter(filters));
    }
  }, [inViewport, initMediaInfo])

  const getDataByFilters = useCallback((filters) => {
    const filter = handleFilter(filters); // 处理筛选条件
    // 筛选条件更新的时候，重置页数，重置是否还有更多数据
    setHasMore(false);
    pageOptRef.current = defaultPageParam; // 重置页数
    initMediaInfo((data) => setMedias(data), filter);
  }, [initMediaInfo])

  useUpdateEffect(() => {
    filterItemRef.current = { ...filterItemRef.current, sort_type: filterItem.sort_type, asc: filterItem.asc }; // 记录之前的筛选条件，用于判断筛选条件是否变化
    initMediaInfo((data) => setMedias(data), handleFilter(filters));
  }, [filterItem])

  useUpdateEffect(() => {
    prevFilters.current = { ...filters }; // 记录之前的筛选条件，用于判断筛选条件是否变化
  }, [filters])

  useUpdateEffect(() => {
    getDataByFilters(filters); // 筛选条件更新的时候，重置页数，重置是否还有更多数据
  }, [filters, getDataByFilters])

  const clearAndRefresh = useCallback(() => {
    setFilters(defaultFiltersByApp); // 重置筛选条件,同时会重置页数

    // 如果页面参数没有变化则手动加载数据
    if (prevFilters.current) {
      if (JSON.stringify(prevFilters.current) === JSON.stringify(defaultFiltersByApp)) {
        getDataByFilters(defaultFiltersByApp);
      }
    }
  }, [getDataByFilters])

  const checkListOnchange = useCallback((val: CheckListValue[], type: 'asc' | 'sort_type') => {
    setFilterItem(prev => {
      let p: filterItemType | any = { ...prev };
      p[type] = val[0];
      return p;
    })

    setShowFloatPanel(false); // 关闭筛选面板
  }, [setShowFloatPanel])

  const toLibraryManagement = useCallback(() => {
    history.push({
      pathname: `/${prefix}/libraryManagement`,
      state: {
        lib_id: state?.lib_id,
        title: state?.title,
        fromLibrary: true
      } as any
    });
  }, [history, prefix, state]);

  const rightSize = useMemo(() => {
    return (
      <div className={styles.right}>
        {/* <PreloadImage src={isDarkMode ? searchIcon_dark : searchIcon} alt="search" /> */}
        <PreloadImage src={isDarkMode ? filterIcon_dark : filterIcon} alt="filter" onClick={() => setShowFloatPanel(true)} />
        <Popover
          className={styles.morePopoverContainer}
          visible={moreVisible}
          onVisibleChange={setMoreVisible}
          content={
            <div className={styles.morePopover}>
              <div className={styles.morePopoverItem} onClick={toLibraryManagement}>
                <span className={styles.morePopoverText}>媒体库管理</span>
              </div>
            </div>
          }
          trigger='click'
          placement='bottom-end'
          style={{ '--arrow-size': '0px' } as React.CSSProperties}
        >
          <PreloadImage src={isDarkMode ? moreIcon_dark : moreIcon} alt="more" onClick={() => setMoreVisible(true)} />
        </Popover>
      </div>
    )
  }, [isDarkMode, moreVisible, toLibraryManagement])

  const toDarmaOrMovie = useCallback((item: any) => {
    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: state.lib_id?.toString() || '0',
      title: state.title
    });

    history.push({
      pathname: `/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`,
      state: {
        ...state, // 保留原有的 state 属性
        fromLibrary: true,
        libraryTitle: state.title,
        classes: item.classes,
        media_id: item.media_id
      }
    });
  }, [history, state])

  useEffect(() => {
    if (isError || medias.length === 0) {
      setCollapse(true); // 如果没有影片数据，则默认收起筛选条件
    }

    // if (medias.length > 0 && pageOptRef.current.offset === 0) {
    //   setCollapse(false); // 如果有影片数据，则默认展开筛选条件
    // }
  }, [isError, medias.length])

  if (!state) { history.goBack(); return; };

  return (
    <>
      <NavigatorBar onBack={() => history.push({
        pathname: `/filmAndTelevisionWall_app/all`
      })} right={rightSize} backIconTheme={isDarkMode ? 'dark' : 'light'} />
      <div className={baseStyles.container_subPage}>
        <div className={baseStyles.tabsHeader}>
          <span className={`${baseStyles.tabsHeader_span} ${baseStyles.path_active}`}>{state.title}</span>
        </div>

        <div className={styles.container} ref={containerRef}>
          {/* 条件筛选 */}
          {(!isError) && <FilmFilter filters={filters} filterTypeList={filterTypeList} controlFilters={setFilters} value={collapse} onChange={setCollapse} />
          }
          <FATErrorComponents refresh={clearAndRefresh} show={isError || filterFilm.length === 0} span={isError ? '获取失败' : '暂无内容'} canTry={isError} subSpan={filterFilm.length === 0 ? '请在媒体库关联的文件夹中添加视频' : undefined}>

            {/* 下拉刷新组件 */}
            <Pull2Refresh onRefresh={clearAndRefresh} isTrigger={collapse} containerRef={containerRef}>
              {/* 影片渲染 */}
              <div className={styles.filter_films_container}>
                {
                  filterFilm.map((item, index) => (
                    <FilterFilmCard type='app' key={item.label + index} title={item.label} subtitle={item.time} score={item.score} cover={item.cover} isLike={item.isLike ? true : false} onCardClick={() => toDarmaOrMovie(item)} />
                  ))
                }
              </div>
            </Pull2Refresh>

            {
              hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
            }

            {/* 弹出浮动面板 */}
            <FloatPanel showFloatPanel={showFloatPanel} setShowFloatPanel={setShowFloatPanel}>
              <div className={styles.filter_float_panel_container}>
                <div className={styles.filter_float_panel_navBar}>
                  <PreloadImage src={isDarkMode ? close_dark : close} alt='close' />
                  <span>筛选</span>
                </div>
                <div className={styles.filter_float_panel_content}>
                  <span className={styles.filter_float_panel_content_list_title}>筛选</span>
                  <div className={styles.filter_float_panel_content_check_list_container}>
                    <CheckList value={[filterItem.sort_type]} onChange={(v) => checkListOnchange(v, 'sort_type')}>
                      <CheckList.Item style={{ color: filterItem.sort_type === 0 ? 'var(--primary-color)' : 'var(--text-color)', background: 'var(--modal-content-background-color)' }} value={0}>添加时间</CheckList.Item>
                      <CheckList.Item style={{ color: filterItem.sort_type === 1 ? 'var(--primary-color)' : 'var(--text-color)', background: 'var(--modal-content-background-color)' }} value={1}>评分</CheckList.Item>
                    </CheckList>
                  </div>
                  <span className={styles.filter_float_panel_content_list_title}>筛选</span>
                  <div className={styles.filter_float_panel_content_check_list_container}>
                    <CheckList value={[filterItem.asc]} onChange={(v) => checkListOnchange(v, 'asc')}>
                      <CheckList.Item style={{ color: filterItem.asc === 0 ? 'var(--primary-color)' : 'var(--text-color)', background: 'var(--modal-content-background-color)' }} value={0}>正序</CheckList.Item>
                      <CheckList.Item style={{ color: filterItem.asc === 1 ? 'var(--primary-color)' : 'var(--text-color)', background: 'var(--modal-content-background-color)' }} value={1}>倒序</CheckList.Item>
                    </CheckList>
                  </div>
                </div>
              </div>
            </FloatPanel>
          </FATErrorComponents>
        </div>
      </div>
    </>
  )
}

export default FATLibrary;