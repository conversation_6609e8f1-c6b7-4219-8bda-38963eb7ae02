import React, { useState, useEffect, useCallback } from 'react';
import { Popup, Button, Toast, Loading } from 'antd-mobile';
import {RightOutline, HeartFill } from 'antd-mobile-icons';
import styles from './index.module.scss';
import close from "@/Resources/camMgmtImg/close.png";
import closeDark from "@/Resources/camMgmtImg/close-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { useRequest } from 'ahooks';
import { getPoolInfo, listDirectory, StoragePool, FileItem as APIFileItem } from '@/api/fatWall';
import { PreloadImage } from '@/components/Image';

interface FileItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  time: string;
  itemCount?: number;
  isLiked?: boolean;
  path: string; // 添加完整路径
  isDirectory?: boolean;
  dataDir?: string; // 存储池的原始data_dir
}

interface FileSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (paths: string[], displayPaths?: string[]) => void;
  title?: string;
}

// 面包屑项目类型
interface BreadcrumbItem {
  id: string;
  name: string;
  path: string;
}

const FileSelector: React.FC<FileSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  title = '选择来源'
}) => {
  const { isDarkMode } = useTheme();
  // 当前文件路径（面包屑）
  const [currentPath, setCurrentPath] = useState<BreadcrumbItem[]>([]);
  
  // 当前显示的文件列表
  const [currentFiles, setCurrentFiles] = useState<FileItem[]>([]);
  
  // 存储池列表
  const [storagePools, setStoragePools] = useState<StoragePool[]>([]);
  
  // webDAV配置信息
  const [webDAVConfig, setWebDAVConfig] = useState<{
    alias_root: string;
  } | null>(null);
  
  // 当前调用 list_directory 时传递的 path.parent 值
  const [currentPathParent, setCurrentPathParent] = useState<string>('');

  // 选中的文件夹路径列表
  const [selectedPaths, setSelectedPaths] = useState<Set<string>>(new Set());

  // 是否已经初始化到第一个存储池
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  
  // 获取存储池信息
  const { run: fetchPoolInfo, loading: poolLoading } = useRequest(
    getPoolInfo,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          setStoragePools(response.data.internal_pool);

          // 保存webDAV配置
          if (response.data.webDAV) {
            setWebDAVConfig({
              alias_root: response.data.webDAV.alias_root
            });
          }

          // 自动进入第一个存储池
          if (response.data.internal_pool.length > 0 && !isInitialized) {
            const firstPool = response.data.internal_pool[0];

            // 设置面包屑为第一个存储池
            const newBreadcrumb: BreadcrumbItem = {
              id: 'pool_0',
              name: firstPool.name,
              path: firstPool.data_dir
            };
            setCurrentPath([newBreadcrumb]);

            // 构造正确的path.parent：data_dir + alias_root
            let pathParent = firstPool.data_dir;
            if (response.data.webDAV?.alias_root) {
              const dataDir = firstPool.data_dir.endsWith('/') ? firstPool.data_dir.slice(0, -1) : firstPool.data_dir;
              const aliasRoot = response.data.webDAV.alias_root;
              pathParent = aliasRoot + dataDir;
            }

            setCurrentPathParent(pathParent);
            setIsInitialized(true);

            // 获取第一个存储池的目录列表
            fetchDirectory({
              path: {
                parent: pathParent,
                recursion: false
              }
            });
          } else {
            // 如果没有存储池或已经初始化过，显示存储池列表
            const poolItems: FileItem[] = response.data.internal_pool.map((pool, index) => ({
              id: `pool_${index}`,
              name: pool.name,
              type: 'folder' as const,
              time: '',
              path: pool.data_dir,
              dataDir: pool.data_dir,
              isDirectory: true
            }));
            setCurrentFiles(poolItems);
            setCurrentPath([]);
          }
        }
      },
      onError: (error) => {
        console.error('获取存储池信息失败：', error);
        Toast.show({
          content: '获取存储池信息失败，请重试',
          position: 'bottom',
          duration: 2000,
        });
        setCurrentFiles([]);
        setCurrentPath([]);
      },
    }
  );

  // 获取目录列表
  const { run: fetchDirectory, loading: directoryLoading } = useRequest(
    listDirectory,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          // 转换API返回的文件列表为本地格式
          const files: FileItem[] = response.data.files
            .filter(file => file.xattr.directory) // 只显示文件夹
            .map((file, index) => ({
              id: `file_${index}`,
              name: file.name,
              type: 'folder' as const,
              time: new Date(parseInt(file.modified_time)).toLocaleDateString(),
              path: `${file.parent}/${file.name}`,
              isDirectory: file.xattr.directory,
              isLiked: file.xattr.favorite
            }));
          setCurrentFiles(files);
        }
      },
      onError: (error) => {
        console.error('获取目录列表失败：', error);
        Toast.show({
          content: '获取目录失败，请重试',
          position: 'bottom',
          duration: 2000,
        });
        // 显示空列表或返回上级目录
        setCurrentFiles([]);
      },
    }
  );
  
  // 初始化时获取存储池信息
  useEffect(() => {
    if (visible) {
      setIsInitialized(false); // 重置初始化状态
      setSelectedPaths(new Set()); // 清空选中状态
      fetchPoolInfo({});
    }
  }, [visible, fetchPoolInfo]);
  
  // 进入文件夹
  const navigateToFolder = useCallback((folder: FileItem) => {

    if (currentPath.length === 0) {
      // 如果是顶层（选择存储池），添加到面包屑并获取该存储池的目录
      const newBreadcrumb: BreadcrumbItem = {
        id: folder.id,
        name: folder.name,
        path: folder.path
      };
      setCurrentPath([newBreadcrumb]);
      
      // 构造正确的path.parent：data_dir + alias_root，避免双斜杠
      let pathParent = folder.path;
      if (webDAVConfig?.alias_root && folder.dataDir) {
        // 确保data_dir末尾没有斜杠
        const dataDir = folder.dataDir.endsWith('/') ? folder.dataDir.slice(0, -1) : folder.dataDir;
        const aliasRoot = webDAVConfig.alias_root 

        pathParent = aliasRoot + dataDir;
      }
      
      // 保存当前的 path.parent 值
      setCurrentPathParent(pathParent);
      
      // 调用目录列表API
      fetchDirectory({
        path: {
          parent: pathParent,
          recursion: false
        }
      });
    } else {
      // 如果已经在某个目录中，继续深入
      const newBreadcrumb: BreadcrumbItem = {
        id: folder.id,
        name: folder.name,
        path: folder.path
      };
      setCurrentPath(prev => [...prev, newBreadcrumb]);
      
      // 保存当前的 path.parent 值
      setCurrentPathParent(folder.path);
      
      // 调用目录列表API
      fetchDirectory({
        path: {
          parent: folder.path,
          recursion: false
        }
      });
    }
  }, [currentPath, fetchDirectory, webDAVConfig]);
  
  // 通过面包屑导航到指定路径
  const navigateToBreadcrumb = useCallback((index: number) => {
    // 如果点击"存储池"（index为-1），直接返回顶层
    if (index === -1) {
      setCurrentPath([]);
      setCurrentPathParent(''); // 清空 path.parent
      fetchPoolInfo({});
      return;
    }
    
    const newPath = currentPath.slice(0, index + 1);
    setCurrentPath(newPath);
    
    if (newPath.length === 0) {
      // 返回到顶层，显示存储池列表
      setCurrentPathParent(''); // 清空 path.parent
      fetchPoolInfo({});
    } else {
      // 获取指定路径的目录列表
      const targetPath = newPath[newPath.length - 1].path;
      
      // 如果是第一层（存储池层级），需要构造特殊的路径
      if (newPath.length === 1) {
        // 找到对应的存储池数据
        const poolData = storagePools.find(pool => pool.name === newPath[0].name);
        if (poolData && webDAVConfig?.alias_root) {
          // 构造正确的path.parent：data_dir + alias_root，避免双斜杠
          const dataDir = poolData.data_dir.endsWith('/') ? poolData.data_dir.slice(0, -1) : poolData.data_dir;
          const aliasRoot = webDAVConfig.alias_root;
          const pathParent = aliasRoot + dataDir;
          
          // 保存当前的 path.parent 值
          setCurrentPathParent(pathParent);
          
          fetchDirectory({
            path: {
              parent: pathParent,
              recursion: false
            }
          });
        }
      } else {
        // 普通的文件夹层级
        // 保存当前的 path.parent 值
        setCurrentPathParent(targetPath);
        
        fetchDirectory({
          path: {
            parent: targetPath,
            recursion: false
          }
        });
      }
    }
  }, [currentPath, fetchPoolInfo, fetchDirectory, storagePools, webDAVConfig]);

  // 切换文件夹选中状态
  const toggleFileSelection = useCallback((file: FileItem, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止冒泡，避免触发导航

    setSelectedPaths(prev => {
      const newSet = new Set(prev);
      if (newSet.has(file.path)) {
        newSet.delete(file.path);
      } else {
        newSet.add(file.path);
      }
      return newSet;
    });
  }, []);
  
  // 选择当前路径
  const handleSelect = useCallback(() => {
    if (selectedPaths.size === 0) {
      Toast.show({
        content: '请至少选择一个文件夹',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }

    // 将选中的路径转换为数组
    const paths = Array.from(selectedPaths);

    // 计算显示路径：根据选中的路径计算对应的显示名称
    const displayPaths = paths.map(path => {
      // 找到对应的文件项
      const file = currentFiles.find(f => f.path === path);
      if (file) {
        // 构建完整的显示路径：存储池名称 + 文件夹名称
        const poolName = currentPath.length > 0 ? currentPath[0].name : '';
        return poolName ? `${poolName}/${file.name}` : file.name;
      }
      return path;
    });

    onSelect(paths, displayPaths);
    onClose();

    // 重置状态
    setCurrentPath([]);
    setCurrentFiles([]);
    setCurrentPathParent('');
    setSelectedPaths(new Set());
    setIsInitialized(false);
  }, [selectedPaths, currentFiles, currentPath, onSelect, onClose]);

  // 关闭弹窗时重置状态
  const handleClose = useCallback(() => {
    setCurrentPath([]);
    setCurrentFiles([]);
    setCurrentPathParent('');
    setSelectedPaths(new Set());
    setIsInitialized(false);
    onClose();
  }, [onClose]);
  
  return (
    <Popup
      visible={visible}
      onMaskClick={handleClose}
      bodyStyle={{
        borderTopLeftRadius: '30px',
        borderTopRightRadius: '30px',
        height:'95%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div className={styles.container}>
        {/* 头部 */}
        <div className={styles.header}>
          <PreloadImage src={isDarkMode ? closeDark : close} alt='close' style={{ width: '40px', height: '40px' }} onClick={handleClose} />
          <span className={styles.title}>{title}</span>
          <div style={{ width: '24px' }} /> {/* 占位元素保持布局平衡 */}
        </div>
        
        {/* 面包屑 */}
        <div className={styles.breadcrumb}>
          {currentPath.length > 0 && (
            <>
              {currentPath.map((pathItem, index) => (
                <React.Fragment key={pathItem.id}>
                  <span
                    className={`${styles.breadcrumbItem} ${index === currentPath.length - 1 ? styles.active : ''}`}
                    onClick={() => navigateToBreadcrumb(index)}
                  >
                    {pathItem.name}
                  </span>
                  {index < currentPath.length - 1 && (
                    <RightOutline className={styles.breadcrumbSeparator} />
                  )}
                </React.Fragment>
              ))}
            </>
          )}
        </div>
        
        {/* 加载状态 */}
        {(poolLoading || directoryLoading) && (
          <div className={styles.loadingContainer}>
            <Loading />
            <span>加载中...</span>
          </div>
        )}
        
        {/* 文件列表 */}
        <div className={styles.fileList}>
          {currentFiles.map(file => (
            <div
              key={file.id}
              className={styles.fileItem}
            >
              <div className={styles.fileIcon}>
                {file.type === 'folder' ? (
                  <div className={styles.folderIcon} />
                ) : (
                  <div className={styles.fileIconImage} />
                )}
              </div>
              <div
                className={styles.fileInfo}
                onClick={() => file.type === 'folder' ? navigateToFolder(file) : null}
              >
                <div className={styles.fileName}>
                  {file.name}
                  {file.isLiked && <HeartFill className={styles.heartIcon} />}
                </div>
                {file.time && (
                  <div className={styles.fileTime}>{file.time}</div>
                )}
                {file.itemCount !== undefined && (
                  <div className={styles.itemCount}>{file.itemCount}个项目</div>
                )}
              </div>
              <div className={styles.checkboxContainer}>
                <input
                  type="checkbox"
                  className={styles.checkbox}
                  checked={selectedPaths.has(file.path)}
                  onChange={(e) => toggleFileSelection(file, e as any)}
                />
              </div>
            </div>
          ))}
          
          {!poolLoading && !directoryLoading && currentFiles.length === 0 && (
            <div className={styles.emptyState}>
              <span>该目录下没有文件夹</span>
            </div>
          )}
        </div>
        
        {/* 底部按钮 */}
        <div className={styles.footer}>
          <Button
            className={styles.selectButton}
            onClick={handleSelect}
            disabled={selectedPaths.size === 0}
            color='primary'
            size='large'
          >
            确定选择 ({selectedPaths.size})
          </Button>
        </div>
      </div>
    </Popup>
  );
};

export default FileSelector; 