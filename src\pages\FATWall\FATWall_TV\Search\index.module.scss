@keyframes collapse {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
    display: none;
    position: absolute;
    left: -100%;
    top: -100%;
  }
}

@keyframes expand {
  from {
    transform: translateX(-100%);
    position: absolute;
    left: -100%;
    top: -100%;
  }
  to {
    display: block;
    transform: translateX(0);
    position: relative;
  }
}

.collapse {
  animation: collapse 0.2s forwards;
}
.expand {
  animation: expand 0.2s forwards;
}

.container {
  width: 100%;
  height: 100%;
  background-color: rgba(38, 38, 38, 1);
  color: #fff;
  display: flex;
  user-select: none;

  .search_container {
    width: 600px;
    height: 100%;
    background-color: rgba(23, 24, 27, 1);
    padding: 120px 80px;

    .search_input {
      width: 100%;
      height: 80px;
      display: flex;
      align-items: center;
      gap: 32px;
      font-family: MiSans;
      font-weight: 400;
      font-size: 30px;
      line-height: 100%;
      letter-spacing: 0px;
      padding: 16px 4px;
      border-bottom: 1px solid rgba(255, 255, 255, 1);

      .search_input_item {
        display: flex;
        align-items: center;
        height: 100%;
        overflow-x: auto;
        scrollbar-width: none;
      }

      img {
        height: 36px;
      }
    }

    .search_buttons {
      width: 100%;
      padding: 36px 0;
      display: flex;
      justify-content: space-between;

      .search_buttons_item {
        transform: scale(1) !important;
        border-radius: 16px;
      }

      .search_buttons_item:focus {
        background-color: var(--primary-color);
        border: none;
      }

      .search_button_item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 140px;
        min-height: 60px;
        color: #fff;
        border: 1px solid;
        border-color: rgba(255, 255, 255, 0.2);
        border-radius: 16px;

        font-family: MiSans;
        font-weight: 400;
        font-size: 30px;
        line-height: 100%;
        letter-spacing: 0px;
      }
    }

    .keyboard_container {
      font-family: MiSans;
      font-weight: 400;
      font-size: 39px;
      line-height: 100%;
      letter-spacing: 0px;
      display: grid;
      grid-template-columns: repeat(6, 70px); /* 每行 10 列 */
      .keyboard_item {
        width: 80px;
        height: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .keyboard_item:focus {
        background-color: var(--primary-color);
        border-radius: 8px;
        color: #fff;
      }
    }
  }

  .search_item_container {
    flex: 1;
    overflow-y: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    display: flex;
    flex-direction: column;

    .gws_container {
      width: 100%;
      height: 100%;
      padding: 0 120px;
      padding-top: 70px;

      .gws_title {
        font-family: MiSans;
        font-weight: 500;
        font-size: 40px;
        line-height: 100%;
        letter-spacing: 0px;
        color: rgba(255, 255, 255, 0.8);
      }

      .gws_content {
        padding-top: 40px;
        padding-bottom: 40px;
        display: grid;
        grid-template-columns: repeat(6, 300px);
        gap: 20px 0;
      }
    }

    .result_not_container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 40px;

      .search_not_result_show_icon {
        width: 94px;
        height: 94px;
        background: rgba(217, 217, 217, 0.4);
      }
      .search_buttons_item:focus {
        background-color: var(--primary-color);
        border-radius: 8px;
      }

      :global {
        .ant-btn {
          width: 240px;
          height: 80px;
          color: #fff;
          border-color: rgba(255, 255, 255, 0.2);
        }
      }

      span {
        font-family: MiSans;
        font-weight: 400;
        font-size: 36px;
        line-height: 100%;
        letter-spacing: 0px;
        color: #fff;
      }
    }

    .result_container {
      width: 100%;
      height: 100%;
      display: flex;

      .result_left {
        width: 450px;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 70px 40px;
        background-color: rgba(31, 30, 30, 0.4);
        color: rgba(255, 255, 255, 1);

        span {
          font-family: MiSans;
          font-weight: 500;
          font-size: 40px;
          line-height: 100%;
          letter-spacing: 0px;
        }

        .result_want_find_content {
          margin-top: 60px;
          display: flex;
          flex-direction: column;
          height: calc(100% - 60px);
          // gap: 20px;
          overflow-y: auto;
          scrollbar-width: none;
          scroll-behavior: smooth;

          > div {
            margin-bottom: 20px;
          }

          .result_want_find_content_item {
            display: flex;
            align-items: center;
            height: 100px;
            padding: 18px;
            border-radius: 20px;
            color: rgba(255, 255, 255, 0.8);
          }

          span {
            width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            font-family: MiSans;
            font-size: 36px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }
        }

        .result_want_find_content_item:focus {
          background-color: var(--primary-color);
          color: #fff;
          border: none;
        }
      }

      .result_right {
        flex: 1;
        overflow: auto;
        scroll-behavior: smooth;
        scrollbar-width: none;
        padding: 50px 120px 0 120px;

        .header {
          width: 100%;
          display: inline-flex;
          align-items: center;
        }

        .category_container {
          display: flex;
          align-items: center;
          height: 120px;
          padding: 16px;
          // gap: 8px;
          > div {
            margin-right: 8px;
          }
        }

        .tabs_item {
          min-width: 130px;
          height: 64px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 45px;
          cursor: pointer;

          span {
            color: #fff;
            font-family: MiSans;
            font-size: 36px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
          }

          &:focus {
            background-color: #fff !important;
            border-radius: 45px;

            span {
              color: #0c0d0e;
              font-family: MiSans;
              font-size: 36px;
              font-style: normal;
              font-weight: 700;
              line-height: normal;
            }
          }
        }

        .tabs_item.selected {
          font-weight: 700;
          background-color: rgba(255, 255, 255, 0.1);
        }

        .tabs_item:focus {
          color: var(--background-color) !important;
          background-color: var(--primary-color);
        }

        .content {
          padding-top: 40px;
          padding-bottom: 10px;
          display: grid;
          grid-template-columns: repeat(6, 265px);
          // gap: 20px 18px;
          > div {
            margin: 0 18px 20px 0;
          }
        }
      }
    }
  }
}
