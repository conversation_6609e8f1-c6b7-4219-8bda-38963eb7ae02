.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  background-color: var(--file-selector-bg);
  width: 100%;
}
.adm-popup-body{
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  
  .title {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-color);
    font-family: MiSans;
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--file-selector-bg);
  overflow-x: auto;
  white-space: nowrap;
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  .breadcrumbItem {
    color: rgba(140, 147, 176, 1);
    background-color: rgba(140, 147, 176, 0.2);
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    
    &.active {
      background-color:rgba(255, 178, 29, 0.2);
      padding: 5px 10px;
      border-radius: 10px;
      color: rgba(255, 178, 29, 1);
      font-weight: 500;
    }
  }
  
  .breadcrumbSeparator {
    margin: 0 4px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 12px;
  }
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
  color: var(--secondary-text-color, #8C93B0);
  
  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.fileList {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }
  
  // 空状态样式
  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 14px;
  }
  
  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    
    &:last-child {
      border-bottom: none;
    }
    
    .fileIcon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .folderIcon {
        width: 100%;
        height: 100%;
        background-color: #FFC14F;
        border-radius: 4px;
        mask-size: cover;
        -webkit-mask-size: cover;
        mask-repeat: no-repeat;
        -webkit-mask-repeat: no-repeat;
        mask-position: center;
        -webkit-mask-position: center;
        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M4 4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V8C22 6.89543 21.1046 6 20 6H12L10 4H4Z' fill='%23FFC14F'/%3E%3C/svg%3E");
        -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M4 4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V8C22 6.89543 21.1046 6 20 6H12L10 4H4Z' fill='%23FFC14F'/%3E%3C/svg%3E");
      }
      
      .fileIconImage {
        width: 100%;
        height: 100%;
        background-color: #599CFA;
        border-radius: 4px;
      }
    }
    
    .fileInfo {
      flex: 1;
      overflow: hidden;
      cursor: pointer;

      .fileName {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: var(--text-color, #000000);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .heartIcon {
          margin-left: 8px;
          color: #FF5C5C;
          font-size: 14px;
        }
      }

      .fileTime {
        font-size: 12px;
        color: var(--secondary-text-color, #8C93B0);
        margin-bottom: 2px;
      }

      .itemCount {
        font-size: 12px;
        color: var(--secondary-text-color, #8C93B0);
      }

      .fileDetails {
        font-size: 12px;
        color: var(--secondary-text-color, #8C93B0);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .checkboxContainer {
      display: flex;
      align-items: center;
      padding-left: 12px;

      .checkbox {
        width: 20px;
        height: 20px;
        border: 2px solid #D1D5DB;
        border-radius: 4px;
        background-color: transparent;
        cursor: pointer;
        position: relative;
        appearance: none;
        -webkit-appearance: none;

        &:checked {
          background-color: #3B82F6;
          border-color: #3B82F6;

          &::after {
            content: '';
            position: absolute;
            left: 6px;
            top: 2px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
          }
        }

        &:hover {
          border-color: #3B82F6;
        }
      }
    }
    
    .rightArrow {
      color: var(--secondary-text-color, #8C93B0);
      margin-left: 8px;
    }
  }
}

.footer {
  padding: 16px;
  // margin-bottom: 58px;
  background-color: var(--file-selector-bg);
  display: flex;
  gap: 12px;

  .cancelButton {
    flex: 1;
    height: 50px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;
    background-color: #F5F5F5;
    color: #666;
    border: none;
  }

  .selectButton {
    flex: 1;
    height: 50px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;

    &:disabled {
      background-color: #E5E5E5;
      color: #B3B3B3;
      border:none;
    }
  }
  
  .confirmButton {
    width: 100%;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    background-color: #32BAC0;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
} 